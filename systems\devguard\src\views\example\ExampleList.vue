<template>
  <div>
    <PageHeader v-if="submitStreamID" :submitStreamID="submitStreamID" @back="goBack">
      <template #extra>
        <div class="flex items-center gap-20px">
          <div class="flex items-center gap-10px">
            <div>实例类型</div><Select v-model:value="exampleType" class="w-100px" :options="exampleTypeList" @change="exampleTypeChange" />
          </div>
          <Button @click="goToExampleConfig()">
            <div class="flex items-center">
              <span>实例配置</span><Icon :icon="RightIcon" class="c-FO-Brand-Primary-Default" />
            </div>
          </Button>
        </div>
      </template>
    </PageHeader>

    <div class="m-20px flex gap-20px b-rd-8px p-20px bg-FO-Container-Fill1!">
      <div class="FO-Font-B18">
        待检查队列
      </div>
      <div>
        <div v-for="item in exampleTypeList" :key="item.value" class="mb-10px flex items-center gap-20px">
          <div class="w-60px font-size-16px">
            {{ item.label }}
          </div>
          <div class="flex gap-10px">
            <div v-for="(user, index) in 3" :key="index" class="b-rd-4px bg-FO-Container-Fill2 p-4px">
              <span class="FO-Font-B14">{{ index + 1 }}</span> <span>{{ user }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="m-20px flex flex-col gap-20px b-rd-8px p-20px bg-FO-Container-Fill1!">
      <div class="flex justify-between">
        <div class="flex items-center gap-10px">
          <div class="h-10px w-10px b-rd-5px" :class="{ 'bg-FO-Datavis-Yellow2': item.status === ExampleStatus.Busy, 'bg-FO-Datavis-Blue2': item.status === ExampleStatus.Idle, 'bg-FO-Content-Text4': item.status === ExampleStatus.Offline }" />
          <div class="FO-Font-B16">
            实例1
          </div>
          <div :class="{ 'c-FO-Datavis-Yellow2': item.status === ExampleStatus.Busy, 'c-FO-Datavis-Blue2': item.status === ExampleStatus.Idle, 'c-FO-Content-Text4': item.status === ExampleStatus.Offline }">
            空闲中
          </div>
          <div class="ml-30px c-FO-Content-Text3">
            类型：通用
          </div>
          <div class="c-FO-Content-Text3">
            已运行1天
          </div>
        </div>
        <div class="b-rd-6px bg-FO-Container-Fill3 px-10px py-6px c-FO-Functional-Error1-Default">
          等待重启中
        </div>
      </div>
      <GanttChart :testGanttItems="testGanttItems" :displayRange="displayRange" @itemClick="itemClick" />
    </div>

    <Slider
      v-model:value="displayRange" class="w-full" :min="1719830400000" :max="1719916800000" range
      :tipFormatter="(percentage) => dayjs(percentage).format('YYYY-MM-DD HH:mm')"
    />
    <ExampleDetailModalHolder />
  </div>
</template>

<script setup lang="ts">
import RightIcon from '@iconify-icons/icon-park-outline/right';
import { Button, Select, Slider } from 'ant-design-vue';
import PageHeader from '../PageHeader.vue';
import GanttChart from './GanttChart.vue';
import { Icon } from '@iconify/vue';
import { onMounted, ref } from 'vue';
import { type GanttItem, PlatformEnterPoint } from '@hg-tech/oasis-common';
import { ExampleStatus } from '../commit-center-home/steams.data';
import dayjs from 'dayjs';
import ExampleDetailModal from './ExampleDetailModal.vue';
import { useLatestPromise, useModalShow } from '@hg-tech/utils-vue';
import { type StreamsListItem, getStreamsInfo } from '../../../src/api';
import { useForgeonConfigStore } from '../../store/modules/forgeonConfig';
import { store } from '../../store/pinia';
import { useRouter } from 'vue-router';
import { TrackEventName } from '../../../src/constants/event';
import { traceClickEvent } from '../../../src/services/track';

const router = useRouter();
const { execute, data: streamsInfoData } = useLatestPromise(getStreamsInfo);
const [ExampleDetailModalHolder, showExampleDetailModal] = useModalShow(ExampleDetailModal);
const forgeonConfig = useForgeonConfigStore(store);
const routeParams = router.currentRoute.value.params;
const submitStreamID = routeParams.submitStreamID ? Number(routeParams.submitStreamID) : null;
const displayRange = ref<[number, number]>([1719830400000, 1719916800000]);
const exampleType = ref();
const streamsInfo = ref<StreamsListItem>();
const item = ref({
  status: ExampleStatus.Busy,
});
const exampleTypeList = [{
  label: '全部',
  value: 1,
}, {
  label: '通用',
  value: 2,
}, {
  label: '非编译',
  value: 3,
}];

const testGanttItems = ref<GanttItem[]>([
  {
    id: '1',
    label: 'Task 1',
    start: 1719830400000, // 2024-07-01 00:00:00 GMT+8
    end: 1719916800000, // 2024-07-02 00:00:00 GMT+8
  },
  {
    id: '2',
    label: 'Task 2',
    start: 1719841200000, // 2024-07-01 03:00:00 GMT+8
    end: 1719862800000, // 2024-07-01 09:00:00 GMT+8
  },
  {
    id: '3',
    label: 'Task 3',
    start: 1719873600000, // 2024-07-01 12:00:00 GMT+8
    end: 1719916800000, // 2024-07-02 00:00:00 GMT+8
  },
  {
    id: '4',
    label: 'Task 4',
    start: 1719873600000, // 2024-07-01 00:00:00 GMT+8
    end: 1719916800000, // 2024-07-02 00:00:00 GMT+8
  },
  {
    id: '5',
    label: 'Task 5',
    start: 1719830400000, // 2024-07-01 00:00:00 GMT+8
    end: 1719862800000, // 2024-07-02 00:00:00 GMT+8
  },
]);

function itemClick(item: GanttItem) {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_DETAIL_CLICK);
  showExampleDetailModal({
  });
}

function exampleTypeChange() {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_STATUS_TYPE_SWITCH);
}
function goToExampleConfig() {
  router.push({
    name: PlatformEnterPoint.ExampleConfig,
    params: {
      submitStreamID,
    },
  });
}

onMounted(async () => {
  if (!submitStreamID) {
    return;
  }
  await execute({ id: forgeonConfig.currentProjectId!, stream_id: submitStreamID }, {});
  streamsInfo.value = streamsInfoData.value?.data?.data?.stream;
});
function goBack() {
  router.push({
    name: PlatformEnterPoint.CommitCenter,
  });
}
</script>

<style lang="less" scoped></style>
