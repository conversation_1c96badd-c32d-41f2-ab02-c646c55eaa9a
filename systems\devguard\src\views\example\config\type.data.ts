export enum workStateType {
// 1 - 空闲；2 - 检查中；3 - 更新中；4 - 重启中；5 - 离线
  Idle = 1,
  Checking = 2,
  Updating = 3,
  Restarting = 4,
  Offline = 5,
}
export enum triggerType {
  // 1 - 触发单次；2 - 周期触发
  Once = 1,
  Periodic = 2,
}

export const workStateOptions = [
  {
    label: '空闲',
    value: workStateType.Idle,
  },
  {
    label: '检查中',
    value: workStateType.Checking,
  },
  {
    label: '更新中',
    value: workStateType.Updating,
  },
  {
    label: '重启中',
    value: workStateType.Restarting,
  },
  {
    label: '离线',
    value: workStateType.Offline,
  },
];

export enum operationTypes {
  // 【 1, 2, 3】 - 预约 【更新、重启、禁用 】；【4，5，6】- 取消预约【更新、重启、禁用】; 【7 8 9】 - 执行【更新、重启、禁用】；10 - 修改；11 - 注册
  Update = 1,
  Restart = 2,
  Disable = 3,
  CancelUpdate = 4,
  CancelRestart = 5,
  CancelDisable = 6,
  ExecuteUpdate = 7,
  ExecuteRestart = 8,
  ExecuteDisable = 9,
  Modify = 10,
  Register = 11,
}
export const operationTypeOptions = [
  {
    label: '预约更新',
    function: '更新',
    action: '预约',
    value: operationTypes.Update,
  },
  {
    label: '预约重启',
    function: '重启',
    action: '预约',
    value: operationTypes.Restart,
  },
  {
    label: '预约禁用',
    function: '禁用',
    action: '预约',
    value: operationTypes.Disable,
  },
  {
    label: '取消更新',
    function: '更新',
    action: '取消',
    value: operationTypes.CancelUpdate,
  },
  {
    label: '取消重启',
    function: '重启',
    action: '取消',
    value: operationTypes.CancelRestart,
  },
  {
    label: '取消禁用',
    function: '禁用',
    action: '取消',
    value: operationTypes.CancelDisable,
  },
  {
    label: '执行更新',
    function: '更新',
    action: '执行',
    value: operationTypes.ExecuteUpdate,
  },
  {
    label: '执行重启',
    function: '重启',
    action: '执行',
    value: operationTypes.ExecuteRestart,
  },
  {
    label: '执行禁用',
    function: '禁用',
    action: '执行',
    value: operationTypes.ExecuteDisable,
  },
  {
    label: '修改实例',
    function: '修改',
    value: operationTypes.Modify,
  },
  {
    label: '注册实例',
    function: '注册',
    value: operationTypes.Register,
  },
];
export const scheduleOperationTypeOptions = [
  {
    label: '更新实例',
    value: operationTypes.Update,
  },
  {
    label: '重启实例',
    value: operationTypes.Restart,
  },
];
