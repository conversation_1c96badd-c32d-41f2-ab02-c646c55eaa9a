<template>
  <Modal
    :width="600"
    :open="show"
    :maskClosable="false"
    destroyOnClose
    centered
    :afterClose="modalDestroy"
    @cancel="() => modalCancel()"
  >
    <template #title>
      <div class="text-center">
        <span class="font-size-[16px] font-bold">
          <span>提交详情</span>
        </span>
      </div>
    </template>
    <div class="flex flex-col gap-10px p-20px">
      <Row>
        <Col :span="4" class="flex justify-end">
          <span class="FO-Font-B16">
            检查状态：
          </span>
        </Col>
        <Col :span="16">
          <span class="font-16">
            12344
          </span>
        </Col>
        <Col :span="4">
          <span class="font-16">
            通过
          </span>
        </Col>
      </Row>
      <Row>
        <Col :span="4" class="flex justify-end">
          <span class="FO-Font-B16">  本地cl：</span>
        </Col>
        <Col :span="16">
          <span class="font-16">
            12344
          </span>
        </Col>
      </Row>
      <Row>
        <Col :span="4" class="flex justify-end">
          <span class="FO-Font-B16">
            提交人：
          </span>
        </Col>
        <Col :span="16">
          <span class="font-16">
            12344
          </span>
        </Col>
      </Row>
      <Row>
        <Col :span="4" class="flex justify-end">
          <span class="FO-Font-B16">
            单号：
          </span>
        </Col>
        <Col :span="16">
          <a class="font-16">
            12344
          </a>
        </Col>
      </Row>
      <Row>
        <Col :span="4" class="flex justify-end">
          <span class="FO-Font-B16">
            提交状态：
          </span>
        </Col>
        <Col :span="16">
          <Process :item="list" />
        </Col>
      </Row>
    </div>
    <template #footer>
      <div class="mt flex justify-end">
        <Button @click="modalDestroy()">
          取消
        </Button>
        <Button type="primary" class="ml-2" @click="handleConfirm">
          确定
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { Button, Col, Modal, Row } from 'ant-design-vue';
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import Process from '../commit-list/Process.vue';

const props = defineProps< ModalBaseProps<void> >();

async function handleConfirm() {
  return props.modalConfirm();
}
const list = {
  ID: 1,
  shelveCL: 9527,
  reviewCL: 9986,
  submitCL: 0,
  serverID: 1,
  streamID: 2,
  swarmBaseURL: 'https://techcentertest.int.hypergryph.com',
  submitterName: 'wanghongyi',
  submitter: {
    ID: 773,
    CreatedAt: '2025-04-27T11:20:05.359+08:00',
    UpdatedAt: '2025-04-27T11:26:24.565+08:00',
    uuid: 'cd7aa65c-ca3c-4a63-b17c-12dd584372ee',
    userName: 'wanghongyi',
    nickName: '王弘毅(mikyi)',
    sideMode: 'dark',
    headerImg: 'https://qmplusimg.henrongyi.top/gva_header.jpg',
    baseColor: '#fff',
    activeColor: '#1890ff',
    authorityId: 888,
    authority: {
      CreatedAt: '0001-01-01T00:00:00Z',
      UpdatedAt: '0001-01-01T00:00:00Z',
      DeletedAt: null,
      authorityId: 0,
      authorityName: '',
      parentId: 0,
      dataAuthorityId: null,
      children: null,
      menus: null,
      defaultRouter: '',
    },
    authorities: null,
    phone: '',
    email: '<EMAIL>',
    enable: 1,
    authType: 1,
    openID: '',
  },
  resCheckState: 4,
  submitState: 1,
  reviewState: 3,
  checkState: 2,
};
</script>
