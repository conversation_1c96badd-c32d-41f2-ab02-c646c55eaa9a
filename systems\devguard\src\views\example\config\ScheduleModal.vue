<template>
  <Modal
    :width="600" :open="show" :maskClosable="false" destroyOnClose centered :afterClose="modalDestroy" :footer="null"
    @cancel="() => modalCancel()"
  >
    <template #title>
      <div class="text-center">
        <span class="font-size-[16px] font-bold">
          <span>定时任务</span>
        </span>
      </div>
    </template>
    <div class="m-[20px]">
      <div class="h-400px">
        <div v-if="!isEdit" class="h-full">
          <div class="flex justify-end">
            <Button shape="round" size="small" @click="addSchedule">
              <div class="flex items-center gap-4px">
                <Icon :icon="addIcon" />
                新增
              </div>
            </Button>
          </div>
          <div v-if="!scheduleList.length" class="h-full flex items-center justify-center">
            暂无定时任务
          </div>
          <div v-else>
            <div v-for="item in scheduleList" :key="item.id" class="mt-10px flex justify-between b-rd-8px bg-FO-Container-Fill3 px-20px py-10px">
              <div>
                <div class="FO-Font-B14">
                  <span class="mr-4px">{{ item.triggerType === 2 ? '周期触发' : dayjs(item.triggerTimestamp).format('YYYY/MM/DD HH:mm') }}</span>
                  <span v-if="item.operationType === 1">更新实例</span>
                  <span v-if="item.operationType === 2">重启实例</span>
                </div>
                <div class="FO-Font-b12">
                  适用实例：{{ item.instanceIDs.join(',') }}
                </div>
              </div>
              <div class="flex gap-10px">
                <Button type="primary" size="small" @click="editSchedule">
                  修改
                </Button>
                <Popconfirm
                  title="确认删除？"
                  okText="确认"
                  cancelText="取消"
                  @confirm="deleteSchedule"
                >
                  <Button type="primary" class="bg-FO-Functional-Error1-Default!" size="small">
                    删除
                  </Button>
                </Popconfirm>
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <Button shape="round" size="small">
            <div class="flex items-center gap-4px">
              <Icon :icon="LeftIcon" />
              <span>返回</span>
            </div>
          </Button>
          <EditSchedule :exampleConfig="exampleConfig" />
        </div>
      </div>
    </div>
  </Modal>
</template>

<script lang="ts" setup>
import { Button, Modal, Popconfirm } from 'ant-design-vue';
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import addIcon from '@iconify-icons/icon-park-outline/plus';
import LeftIcon from '@iconify-icons/icon-park-outline/left';
import { Icon } from '@iconify/vue';
import { ref } from 'vue';
import { traceClickEvent } from '../../../services/track';
import { TrackEventName } from '../../../constants/event';
import type { ExampleConfigItem, scheduleItem } from '../../../api';
import EditSchedule from './EditSchedule.vue';
import dayjs from 'dayjs';

const props = defineProps<ModalBaseProps<{ updatedItem?: any }> & {
  scheduleList?: scheduleItem[];
  exampleConfig: ExampleConfigItem[];
}>();
const isEdit = ref(false);
const scheduleList = ref(props.scheduleList ?? [{
  id: 1,
  operationType: 1, // 1 - 更新实例；2 - 重启实例
  triggerType: 2, // 1 - 触发单次；2 - 周期触发
  triggerTimestamp: 0, // 单次触发时间戳，要求晚于当前时间
  triggerCron: '1 0 * * 4', // 周期触发 cron 表达式
  instanceIDs: [ // 实例 id 数组
    1,
  ],
}]);
function addSchedule() {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_SCHEDULE_ADD);
  isEdit.value = true;
}
function editSchedule() {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_SCHEDULE_MODIFY);
  isEdit.value = true;
}
function deleteSchedule() {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_SCHEDULE_DELETE);
}
</script>

<style lang="less" scoped>
.file-select :deep(.ant-select-selector) {
  min-height: 60px;
  align-items: flex-start;
}
</style>
