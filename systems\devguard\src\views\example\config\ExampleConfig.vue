<template>
  <div>
    <PageHeader v-if="submitStreamID" :submitStreamID="submitStreamID" @back="goBack">
      <template #extra>
        <div class="flex items-center gap-20px">
          <Popconfirm title="确认操作？" okText="确认" cancelText="取消" @confirm="handleBulkUpdate">
            <Button shape="round" @click="batchOperate(1)">
              <div class="flex items-center">
                <span>全部更新</span>
              </div>
            </Button>
          </Popconfirm>
          <Popconfirm title="确认操作？" okText="确认" cancelText="取消" @confirm="handleBulkRestart">
            <Button shape="round" @click="batchOperate(2)">
              <div class="flex items-center">
                <span>全部重启</span>
              </div>
            </Button>
          </Popconfirm>
          <Button shape="round" @click="handleShowSchedule">
            <div class="flex items-center">
              <span>定时任务</span>
            </div>
          </Button>
          <Button shape="round" @click="handleEditRule">
            <div class="flex items-center">
              <span>实例分配规则</span>
            </div>
          </Button>
          <Button shape="round" @click="handleShowOperation">
            <div class="flex items-center">
              <span>操作记录</span>
            </div>
          </Button>
        </div>
      </template>
    </PageHeader>
    <div class="example-config-list">
      <div
        v-for="item in exampleConfig" :key="item.id"
        class="example-config-list-item mx-20px my-10px flex justify-between b-rd-8px p-20px bg-FO-Container-Fill1!"
      >
        <div class="flex items-center">
          <DragOutlined class="list-drag-btn hidden h-10px w-10px cursor-grab" />
          <div class="list-status-dot h-10px w-10px b-rd-5px" :class="getDotClass(item.workState, item.disabled!)" />
          <div class="ml-10px">
            <div class="flex items-center gap-10px">
              <span class="FO-Font-B16">{{ item.name || '未命名' }}</span>
              <Icon :icon="editIcon" class="cursor-pointer" @click="editExampleDetail(item)" />
            </div>
            <div class="flex gap-20px c-FO-Content-Text3">
              <span>类型：{{ item.instanceType === instanceType.Common ? '通用' : item.instanceType === instanceType.Compile
                ? '编译' : '非编译' }}</span>
              <span>Workspace:{{ item.workspace }}</span>
              <span>IP:{{ item.ipAddress }}</span>
            </div>
          </div>
        </div>

        <div class="flex items-center gap-10px">
          <div class="flex gap-10px">
            <span v-if="item.disabled">实例已禁用</span>
            <span>{{ workStateOptions.find(i => i.value === item.workState)?.label }}</span>
          </div>
          <div class="flex gap-10px">
            <Button @click="exampleUpdate(item.id)">
              更新
            </Button>
            <Button @click="exampleRestart(item.id)">
              重启
            </Button>
            <Button @click="exampleDisable(item.id)">
              禁用
            </Button>
          </div>
        </div>
      </div>
    </div>
    <AssignRuleModalHolder />
    <ExampleDetailDrawerHolder />
    <OperationDrawerHolder />
    <ScheduleModalHolder />
  </div>
</template>

<script setup lang="ts">
import { DragOutlined } from '@ant-design/icons-vue';
import PageHeader from '../../PageHeader.vue';
import { useRouter } from 'vue-router';
import { Button, Popconfirm } from 'ant-design-vue';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import { nextTick, onMounted, ref } from 'vue';
import { useSortable } from '../../../hooks/useSortable';
import editIcon from '@iconify-icons/icon-park-outline/edit';
import { Icon } from '@iconify/vue';
import AssignRuleModal from './AssignRuleModal.vue';
import { useLatestPromise, useModalShow } from '@hg-tech/utils-vue';
import ExampleDetailDrawer from './ExampleDetailDrawer.vue';
import OperationDrawer from './OperationDrawer.vue';
import ScheduleModal from './ScheduleModal.vue';
import { TrackEventName } from '../../../constants/event';
import { traceClickEvent } from '../../../services/track';
import { type ExampleConfigItem, batchSortListApi, getDistributeApi, getExampleConfigListApi, getScheduleListApi, setDistributeApi, updateExamplebatchOperateApi, updateExampleConfigItemApi, updateExampleOperateApi } from '../../../api';
import { useForgeonConfigStore } from '../../../store/modules/forgeonConfig';
import { store } from '../../../store/pinia';
import { workStateOptions, workStateType } from './type.data';
import { instanceType } from '../type.data';
import { buildUUID } from '../../../utils/uuid';

const router = useRouter();
const routeParams = router.currentRoute.value.params;
const forgeonConfig = useForgeonConfigStore(store);
const exampleConfig = ref<ExampleConfigItem[]>([]);
const [AssignRuleModalHolder, showAssignRuleModal] = useModalShow(AssignRuleModal);
const [ExampleDetailDrawerHolder, showExampleDetailDrawer] = useModalShow(ExampleDetailDrawer);
const [OperationDrawerHolder, showOperationRecordModal] = useModalShow(OperationDrawer);
const [ScheduleModalHolder, showScheduleModal] = useModalShow(ScheduleModal);
const { execute: exampleConfigExecute, data: exampleConfigList } = useLatestPromise(getExampleConfigListApi);
const { execute: batchSortListExecute } = useLatestPromise(batchSortListApi);
const { execute: getScheduleListExecute, data: scheduleList } = useLatestPromise(getScheduleListApi);
const { execute: updateExamplebatchOperateExecute } = useLatestPromise(updateExamplebatchOperateApi);
const { execute: updateExampleConfigItemExecute, data: updateExampleConfigItemRes } = useLatestPromise(updateExampleConfigItemApi);
const { execute: updateExampleOperateExecute } = useLatestPromise(updateExampleOperateApi);
const { execute: getDistributeExecute, data: distributeItem } = useLatestPromise(getDistributeApi);
const { execute: setDistributeExecute } = useLatestPromise(setDistributeApi);
const submitStreamID = routeParams.submitStreamID ? Number(routeParams.submitStreamID) : null;
async function handleEditRule() {
  await getDistributeExecute({ id: forgeonConfig.currentProjectId!, streamID: submitStreamID! }, {});
  await showAssignRuleModal({
    distributeType: distributeItem.value?.data?.data?.distributeType,
    ruleList: distributeItem.value?.data?.data?.ruleList.map((item) => ({ ...item, id: buildUUID() })),
    async sentReq(distributeType: number, ruleList: any[]) {
      const res = await setDistributeExecute({ id: forgeonConfig.currentProjectId! }, { streamID: submitStreamID!, distributeType, ruleList });
      return res?.data?.data;
    },
  });
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_ALLOCATION_SAVE);
}
function getDotClass(workState: number | undefined, disabled: boolean) {
  if (!workState) {
    return '';
  }
  if (disabled) {
    return 'bg-FO-Functional-Error1-Default';
  } else {
    switch (workState) {
      case workStateType.Checking:
      case workStateType.Updating:
      case workStateType.Restarting:
        return 'bg-FO-Datavis-Yellow2';
      case workStateType.Offline:
        return 'bg-FO-Content-Text4';
      case workStateType.Idle:
        return 'bg-FO-Datavis-Blue2';
      default:
        return 'bg-FO-Datavis-Yellow2';
    }
  }
}
async function handleShowSchedule() {
  await getScheduleListExecute({ id: forgeonConfig.currentProjectId!, streamID: submitStreamID! }, {});
  await showScheduleModal({
    scheduleList: scheduleList.value?.data?.data,
    exampleConfig: exampleConfig.value,

  });
}
function handleShowOperation() {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_OPERATION_LOG_CLICK);
  showOperationRecordModal({});
}
async function getExampleConfigList() {
  await exampleConfigExecute({ id: forgeonConfig.currentProjectId!, streamID: submitStreamID! }, {});
  exampleConfig.value = exampleConfigList.value?.data?.data || [];
}

function goBack() {
  router.push({
    name: PlatformEnterPoint.Example,
    params: {
      submitStreamID,
    },
  });
}

async function batchOperate(operation: number) {
  await updateExamplebatchOperateExecute({ id: forgeonConfig.currentProjectId! }, { streamID: submitStreamID!, operation });
  await getExampleConfigList();
}
async function editExampleDetail(item: ExampleConfigItem) {
  await showExampleDetailDrawer({
    item,
    async sentReq(name: string) {
      await updateExampleConfigItemExecute({ id: forgeonConfig.currentProjectId! }, { streamID: submitStreamID!, instanceID: item.id!, name });
      return updateExampleConfigItemRes.value?.data?.data;
    },
  });
  await getExampleConfigList();
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_EDIT_SAVE);
}
function handleBulkUpdate() {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_BULK_UPDATE_CLICK);
}
function handleBulkRestart() {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_BULK_RESTART_CLICK);
}
async function updateExampleOperate(instanceID: number, operation: number) {
  await updateExampleOperateExecute({ id: forgeonConfig.currentProjectId! }, { streamID: submitStreamID!, instanceID: instanceID!, operation });
  await getExampleConfigList();
}
async function exampleUpdate(instanceID: number) {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_SINGLE_UPDATE_CLICK);
  await updateExampleOperate(instanceID, 1);
}
async function exampleRestart(instanceID: number) {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_SINGLE_RESTART_CLICK);
  await updateExampleOperate(instanceID, 2);
}
async function exampleDisable(instanceID: number) {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_SINGLE_DISABLE_CLICK);
  await updateExampleOperate(instanceID, 3);
}
// 初始化拖拽
function initDrag() {
  nextTick(() => {
    const el = document.querySelector(`.example-config-list`) as HTMLElement;
    useSortable(el, {
      handle: `.list-drag-btn`,
      onEnd: async ({ oldIndex, newIndex }: { oldIndex?: number; newIndex?: number }) => {
        if (oldIndex === undefined || newIndex === undefined || oldIndex === newIndex) {
          return;
        }
        const temp = exampleConfig.value.map((item) => item.id) as number[];
        const current = temp[oldIndex];
        temp.splice(oldIndex, 1);
        temp.splice(newIndex, 0, current);
        await batchSortListExecute({ id: forgeonConfig.currentProjectId! }, { streamID: submitStreamID!, idList: temp });
      },
    });
  });
}
onMounted(async () => {
  await getExampleConfigList();
  initDrag();
});
</script>

<style lang="less" scoped>
.example-config-list {
  max-height: calc(100vh - 200px);
  overflow: auto;

  .example-config-list-item {
    &:hover {
      .list-drag-btn {
        display: block;
      }

      .list-status-dot {
        display: none;
      }
    }
  }
}
</style>
