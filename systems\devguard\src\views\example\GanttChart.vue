<template>
  <div class="w-full flex flex-col items-center gap-[20px] py-[10px]">
    <GanttChart
      class="w-full rd-[12px] bg-FO-Container-Fill3"
      :items="testGanttItems"
      :min="displayRange[0]"
      :max="displayRange[1]"
      :axisLabelFormat="(percentage) => {
        return dayjs(percentage).format('HH:mm');
      }"
      :rowHeight="15"
      :rowGap="10"
      :tickInterval="tickInterval"
      :precision="60 * 1000 * 10"
      :renderItem="renderItem"
      itemClass="hover:scale-101"
      axisDividerClass="bg-FO-Container-Fill4"
    />
  </div>
</template>

<script lang="tsx" setup>
import { type GanttItem, FollowTooltip, GanttChart } from '@hg-tech/oasis-common';
import dayjs from 'dayjs';

import { type PropType, computed } from 'vue';

const props = defineProps({
  testGanttItems: {
    type: Array as PropType<GanttItem[]>,
    required: true,
  },
  displayRange: {
    type: Array as unknown as PropType<[number, number]>,
    default: () => [0, 0],
  },
});

const emits = defineEmits<{
  (e: 'itemClick', value: GanttItem): void;
}>();

function itemClick(item: GanttItem) {
  emits('itemClick', item);
}
const tickInterval = computed(() => {
  const range = props.displayRange[1] - props.displayRange[0];
  // 以小时为单位，间隔为1小时或2小时（以毫秒为单位）
  const oneHour = 3600 * 1000;
  return range < 24 * oneHour ? oneHour : 2 * oneHour;
});

function renderItem(item: GanttItem) {
  return (
    <FollowTooltip offset={-50} tooltipStyle={{ minWidth: '120px' }}>
      {{

        default: () => (
          <div class="h-full w-full flex items-center justify-center bg-red" onClick={() => itemClick(item)}>
            <span class="text-FO-Text-Primary text-[12px]">{item.label}</span>
          </div>
        ),
        content: () => (
          <div>
            <div class="text-FO-Text-Primary text-[12px]">{item.label}</div>
            <div class="text-FO-Text-Secondary text-[12px]">
              {dayjs(item.start).format('HH:mm')} - {dayjs(item.end).format('HH:mm')}
            </div>
            <div class="text-FO-Text-Secondary text-[12px]">长度: {item.end - item.start} ms</div>
          </div>
        ),
      }}
    </FollowTooltip>
  );
}
</script>
