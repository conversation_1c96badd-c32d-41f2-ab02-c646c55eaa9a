<template>
  <div class="streams-list">
    <div v-for="stream in streamsList" :key="stream.ID" class="streams-list-item">
      <div class="flex items-center">
        <MainlineIcon v-if="stream.streamType === StreamType.Mainline" class="h-20px" />
        <ReleaseIcon v-else-if="stream.streamType === StreamType.Release" class="h-20px" />
        <TaskIcon v-else-if="stream.streamType === StreamType.Task" class="h-20px" />
        <VirtualIcon v-else-if="stream.streamType === StreamType.Virtual" class="h-20px" />
        <DevelopmentIcon v-else class="h-20px" />

        <div class="ml-16px">
          <div class="flex items-center font-bold">
            {{ stream.description || stream.path }}
          </div>
          <div class="flex items-center text-xs c-FO-Content-Text2">
            <span>{{ stream.path }}</span>
          </div>
        </div>
      </div>
      <div class="flex">
        <div class="b-r-1px b-r-FO-Container-Stroke4 px-10px">
          <div class="flex cursor-pointer items-center gap-10px b-rd-8px p-10px hover:bg-FO-Container-Fill3" @click="goToCommitList(stream)">
            <div class="FO-Font-b18">
              正在提交
            </div>
            <div class="FO-Font-B18">
              {{ stream.submittingCount }}
            </div>
          </div>
        </div>
        <div class="b-r-FO-Container-Stroke4 px-10px">
          <div class="flex cursor-pointer items-center gap-10px b-rd-8px p-10px hover:bg-FO-Container-Fill3" @click="goToExample(stream)">
            <div class="FO-Font-b18">
              检查实例状态
            </div>
            <div
              v-for="(item, index) in stream.instances?.filter((instanceItem) => instanceItem.compile)" :key="index"
              class="h-10px w-10px b-rd-5px"
              :class="{ 'bg-FO-Datavis-Yellow2': item.status === ExampleStatus.Busy, 'bg-FO-Datavis-Blue2': item.status === ExampleStatus.Idle, 'bg-FO-Content-Text4': item.status === ExampleStatus.Offline }"
            />
            <div class="h-20px w-1px bg-FO-Container-Stroke4" />
            <div
              v-for="(item, index) in stream.instances?.filter((instanceItem) => !instanceItem.compile)" :key="index"
              class="h-10px w-10px b-rd-5px"
              :class="{ 'bg-FO-Datavis-Yellow2': item.status === ExampleStatus.Busy, 'bg-FO-Datavis-Blue2': item.status === ExampleStatus.Idle, 'bg-FO-Content-Text4': item.status === ExampleStatus.Offline }"
            />
            <div class="FO-Font-b18">
              待检查队列
            </div>
            <div class="FO-Font-B18">
              {{ stream.instanceCount }}
            </div>
          </div>
        </div>
        <!-- 暂时隐藏 -->
        <!-- <div class="px-10px">
          <div class="flex cursor-pointer items-center gap-10px b-rd-8px p-10px hover:bg-FO-Container-Fill2">
            <div class="FO-Font-b18">
              今日已提交
            </div>
            <div class="FO-Font-B18">
              {{ stream.todaySubmittedCount }}
            </div>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';
import type { StreamsListItem } from '../../api';
import DevelopmentIcon from '../../assets/icons/stream-development.svg?component';
import MainlineIcon from '../../assets/icons/stream-mainline.svg?component';
import ReleaseIcon from '../../assets/icons/stream-release.svg?component';
import TaskIcon from '../../assets/icons/stream-task.svg?component';
import VirtualIcon from '../../assets/icons/stream-virtual.svg?component';
import { ExampleStatus, StreamType } from './steams.data';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';

withDefaults(defineProps<{
  streamsList: StreamsListItem[];
}>(), {
  streamsList: () => [],
});
const router = useRouter();
function goToCommitList(stream: StreamsListItem) {
  router.push({
    name: PlatformEnterPoint.CommitList,
    params: {
      submitStreamID: stream.submitStreamID,
    },
  });
}
function goToExample(stream: StreamsListItem) {
  router.push({
    name: PlatformEnterPoint.Example,
    params: {
      submitStreamID: stream.submitStreamID,
    },
  });
}
</script>

<style lang="less" scoped>
@import (reference) '@hg-tech/forgeon-style/vars.less';

.streams-list {
  &-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 16px;
    border: 1px solid @FO-Container-Stroke1;
    border-radius: 8px;
    background-color: @FO-Container-Fill1;

    &:hover {
      border-color: @FO-Brand-Primary-Default;
    }

    &:not(:last-child) {
      margin-bottom: 8px;
    }
  }
}
</style>
