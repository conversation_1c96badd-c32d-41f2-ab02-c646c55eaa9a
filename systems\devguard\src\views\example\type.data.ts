export enum instanceType {
// 1 - 通用；2 - 编译；3 - 非编译
  Common = 1,
  Compile = 2,
  NonCompile = 3,
}
export const instanceTypeOptions = [
  {
    label: '通用',
    value: instanceType.Common,
  },
  {
    label: '编译',
    value: instanceType.Compile,
  },
  {
    label: '非编译',
    value: instanceType.NonCompile,
  },
];
export const instanceTypeAnotherOptions = [
  {
    label: '全部',
    value: instanceType.Common,
  },
  {
    label: '代码',
    value: instanceType.Compile,
  },
  {
    label: '资产',
    value: instanceType.NonCompile,
  },
];
