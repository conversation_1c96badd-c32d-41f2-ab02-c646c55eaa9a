<template>
  <Modal
    :width="600" :open="show" :maskClosable="false" destroyOnClose centered :afterClose="modalDestroy"
    @cancel="() => modalCancel()"
  >
    <template #title>
      <div class="text-center">
        <span class="font-size-[16px] font-bold">
          <span>检查实例分配规则</span>
        </span>
      </div>
    </template>
    <div class="m-[20px]">
      <RadioGroup v-model:value="distributeType">
        <Radio class="h-30px flex line-height-30px" :value="1">
          <span>平均分配</span>
        </Radio>
        <Radio class="h-30px flex line-height-30px" :value="2">
          <div class="flex items-center gap-10px">
            <span>按条件分配</span>
            <Button class="flex items-center" size="small" type="primary" @click="addRule">
              <Icon :icon="addIcon" /><span>添加条件</span>
            </Button>
          </div>
        </Radio>
      </RadioGroup>
      <div class="assign-rule-container">
        <div v-for="(item, index) in ruleList" :key="item.id" class="relative mt-10px b-rd-8px bg-FO-Container-Fill3 p-20px">
          <Button class="absolute right-0 top-0 flex transform-translate-x-[50%] transform-translate-y-[-50%] items-center h-16px! bg-FO-Functional-Error1-Default! p-0!" size="small" type="primary" @click="deleteRule(index)">
            <Icon :icon="deleteIcon" />
          </Button>

          <div class="flex items-center gap-20px">
            <DragOutlined class="drag-btn h-10px w-10px cursor-grab" />
            <div class="flex flex-1 flex-col gap-5px">
              <div class="flex">
                <span class="w-120px">若文件后缀包含：</span> <Select
                  v-model:value="item.suffixList" class="file-select min-h-60px flex-1"
                  :open="false" mode="tags" style="width: 100%" :tokenSeparators="[',']" @change="suffixListChange(index)"
                />
              </div>
              <div class="flex">
                <span class="w-120px">则触发实例类型：</span><Select v-model:value="item.instanceType" :options="instanceTypeOptions" class="w-200px" />
              </div>
            </div>
          </div>
        </div>
        <div class="mt-10px bg-FO-Container-Fill3 px-20px py-10px">
          若不满足上述条件，则触发其他实例
        </div>
      </div>
    </div>
    <div v-if="errorText" class="text-center text-FO-Functional-Error1-Default">
      {{ errorText }}
    </div>
    <template #footer>
      <div class="mt flex justify-end">
        <Button type="primary" @click="handleConfirm">
          保存
        </Button>
        <Button class="ml-2" @click="modalDestroy()">
          取消
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { Button, Modal, Radio, RadioGroup, Select } from 'ant-design-vue';
import { type ModalBaseProps, useLatestPromise } from '@hg-tech/utils-vue';
import { nextTick, onMounted, ref } from 'vue';
import addIcon from '@iconify-icons/icon-park-outline/plus';
import deleteIcon from '@iconify-icons/icon-park-outline/minus';
import { Icon } from '@iconify/vue';
import { DragOutlined } from '@ant-design/icons-vue';
import { useSortable } from '../../../hooks/useSortable';
import { instanceTypeOptions } from '../type.data';
import { setDistributeApi } from '../../../api';
import { useForgeonConfigStore } from '../../../store/modules/forgeonConfig';
import { store } from '../../../store/pinia';
import { useRouter } from 'vue-router';
import { buildUUID } from '../../../utils/uuid';

const props = defineProps<ModalBaseProps<{ updatedItem?: null | undefined }> & {
  distributeType?: number;
  ruleList?: {
    suffixList: string[];
    instanceType: number;
    id: string;
  }[];
  sentReq: (distributeType: number, ruleList: { suffixList: string[]; instanceType: number }[]) => Promise<null | undefined>;
}>();
const router = useRouter();
const routeParams = router.currentRoute.value.params;
const submitStreamID = routeParams.submitStreamID ? Number(routeParams.submitStreamID) : null;
const { execute: setDistributeExecute } = useLatestPromise(setDistributeApi);
const forgeonConfig = useForgeonConfigStore(store);
const errorText = ref('');
const distributeType = ref(props.distributeType ?? 1);
const ruleList = ref(props.ruleList ?? []);
function suffixListChange(index: number) {
  ruleList.value[index].suffixList = ruleList.value[index].suffixList.map((item) => {
    if (!item || item.trim() === '') {
      return '';
    }
    if (item[0] !== '.') {
      return `.${item}`;
    } else {
      return item;
    }
  });
}

function addRule() {
  ruleList.value.push({
    suffixList: [],
    instanceType: 1,
    id: buildUUID(),
  });
}

function deleteRule(index: number) {
  ruleList.value.splice(index, 1);
}
async function handleConfirm() {
  errorText.value = '';
  if (distributeType.value === 2) {
    if (!ruleList.value.length) {
      errorText.value = '请至少添加一个条件';
      return;
    }
    if (ruleList.value.some((item) => !item.suffixList?.length)) {
      errorText.value = '请将条件配置完整';
      return;
    }
  }
  const updatedItem = await props.sentReq(distributeType.value, ruleList.value);
  return props.modalConfirm({ updatedItem });
}

function initDrag() {
  nextTick(() => {
    const el = document.querySelector(`.assign-rule-container`) as HTMLElement;
    useSortable(el, {
      handle: `.drag-btn`,
      onEnd: async ({ oldIndex, newIndex }: { oldIndex?: number; newIndex?: number }) => {
        if (oldIndex === undefined || newIndex === undefined || oldIndex === newIndex) {
          return;
        }
        const temp = ruleList.value;
        const current = temp[oldIndex];
        temp.splice(oldIndex, 1);
        temp.splice(newIndex, 0, current);
        if (!submitStreamID) {
          return;
        }
        await setDistributeExecute({ id: forgeonConfig.currentProjectId! }, {
          streamID: submitStreamID,
          distributeType: distributeType.value,
          ruleList: temp,
        });
      },
    });
  });
}
onMounted(() => {
  initDrag();
});
</script>

<style lang="less" scoped>
.file-select :deep(.ant-select-selector) {
  min-height: 60px;
  align-items: flex-start;
}
</style>
